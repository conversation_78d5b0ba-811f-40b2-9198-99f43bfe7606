<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FunASR 说话人识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        
        .results {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .result-item {
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .speaker-0 { border-left-color: #007bff; background-color: #e3f2fd; }
        .speaker-1 { border-left-color: #28a745; background-color: #e8f5e9; }
        .speaker-2 { border-left-color: #ffc107; background-color: #fff8e1; }
        .speaker-3 { border-left-color: #dc3545; background-color: #ffebee; }
        .speaker-unknown { border-left-color: #6c757d; background-color: #f8f9fa; }
        
        .speaker-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .config {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .config input {
            margin: 5px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 FunASR 说话人识别测试</h1>
        
        <div class="config">
            <h3>服务器配置</h3>
            <label>服务器地址: <input type="text" id="serverHost" value="localhost"></label>
            <label>端口: <input type="number" id="serverPort" value="10095"></label>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary" onclick="connect()">连接服务器</button>
            <button id="disconnectBtn" class="btn-secondary" onclick="disconnect()" disabled>断开连接</button>
            <button id="startBtn" class="btn-success" onclick="startRecording()" disabled>开始录音</button>
            <button id="stopBtn" class="btn-danger" onclick="stopRecording()" disabled>停止录音</button>
            <button id="clearBtn" class="btn-secondary" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="results" id="results">
            <div class="result-item">
                <div class="speaker-info">等待连接服务器...</div>
                <div>请先连接到 FunASR WebSocket 服务器</div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioStream = null;
        let isRecording = false;
        
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        
        function updateStatus(message, className) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${className}`;
        }
        
        function addResult(text, speakerId, isFinal, mode) {
            const resultItem = document.createElement('div');
            resultItem.className = `result-item speaker-${speakerId >= 0 ? speakerId : 'unknown'}`;
            
            const speakerInfo = document.createElement('div');
            speakerInfo.className = 'speaker-info';
            speakerInfo.textContent = `说话人 ${speakerId >= 0 ? speakerId : '未知'} | ${mode || '未知模式'} | ${isFinal ? '最终结果' : '实时结果'}`;
            
            const textDiv = document.createElement('div');
            textDiv.textContent = text;
            
            resultItem.appendChild(speakerInfo);
            resultItem.appendChild(textDiv);
            
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function connect() {
            const host = document.getElementById('serverHost').value;
            const port = document.getElementById('serverPort').value;
            const url = `ws://${host}:${port}`;
            
            updateStatus('正在连接...', 'connecting');
            
            websocket = new WebSocket(url);
            
            websocket.onopen = function(event) {
                updateStatus('已连接', 'connected');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startBtn.disabled = false;
                
                // 发送初始配置消息
                const initMessage = {
                    "mode": "2pass",
                    "chunk_size": [5, 10, 5],
                    "chunk_interval": 10,
                    "wav_name": "web_client",
                    "is_speaking": true,
                    "wav_format": "PCM",
                    "audio_fs": 16000
                };
                
                console.log('发送初始消息:', initMessage);
                websocket.send(JSON.stringify(initMessage));
                
                addResult('WebSocket 连接成功，已发送初始配置', -1, false, 'system');
            };
            
            websocket.onmessage = function(event) {
                try {
                    const response = JSON.parse(event.data);
                    console.log('收到响应:', response);
                    
                    if (response.text && response.text.trim()) {
                        const speakerId = response.speaker_id !== undefined ? response.speaker_id : -1;
                        const isFinal = response.is_final || false;
                        const mode = response.mode || 'unknown';
                        
                        addResult(response.text, speakerId, isFinal, mode);
                        
                        // 检查是否包含说话人信息
                        if (speakerId >= 0) {
                            console.log(`✅ 检测到说话人 ${speakerId}: ${response.text}`);
                        } else {
                            console.log(`⚠️ 未检测到说话人信息: ${response.text}`);
                        }
                    }
                } catch (e) {
                    console.log('收到非JSON消息（可能是音频数据）');
                }
            };
            
            websocket.onclose = function(event) {
                updateStatus('连接已断开', 'disconnected');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                startBtn.disabled = true;
                stopBtn.disabled = true;
                
                addResult('WebSocket 连接已断开', -1, false, 'system');
            };
            
            websocket.onerror = function(error) {
                updateStatus('连接错误', 'disconnected');
                console.error('WebSocket 错误:', error);
                addResult('WebSocket 连接错误', -1, false, 'error');
            };
        }
        
        function disconnect() {
            if (websocket) {
                websocket.close();
            }
            if (isRecording) {
                stopRecording();
            }
        }
        
        async function startRecording() {
            try {
                audioStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: false,
                        noiseSuppression: false
                    } 
                });
                
                mediaRecorder = new MediaRecorder(audioStream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0 && websocket && websocket.readyState === WebSocket.OPEN) {
                        // 注意：这里发送的是 WebM 格式，服务器需要支持
                        // 实际应用中可能需要转换为 PCM 格式
                        websocket.send(event.data);
                    }
                };
                
                mediaRecorder.start(100); // 每100ms发送一次数据
                isRecording = true;
                
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
                addResult('开始录音...', -1, false, 'system');
                
            } catch (error) {
                console.error('录音启动失败:', error);
                addResult('录音启动失败: ' + error.message, -1, false, 'error');
            }
        }
        
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                startBtn.disabled = false;
                stopBtn.disabled = true;
                
                // 发送结束消息
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    const endMessage = {
                        "is_speaking": false,
                        "wav_name": "web_client"
                    };
                    websocket.send(JSON.stringify(endMessage));
                }
                
                addResult('录音已停止', -1, false, 'system');
            }
            
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            addResult('页面已加载，请连接到 FunASR 服务器', -1, false, 'system');
        };
        
        // 页面关闭时清理资源
        window.onbeforeunload = function() {
            if (isRecording) {
                stopRecording();
            }
            if (websocket) {
                websocket.close();
            }
        };
    </script>
</body>
</html>
