#!/usr/bin/env python3
"""
简单的WebSocket服务器启动示例
基于FunASR的WebSocket服务器代码进行修改
"""

import asyncio
import websockets
import argparse

async def simple_echo_server(websocket):
    """简单的回显WebSocket服务器"""
    print(f"客户端连接: {websocket.remote_address}")
    try:
        async for message in websocket:
            print(f"收到消息: {message}")
            await websocket.send(f"服务器收到: {message}")
    except websockets.ConnectionClosed:
        print("客户端断开连接")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动WebSocket服务器")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=10095, help="服务器端口")
    args = parser.parse_args()
    
    # 启动WebSocket服务器
    server = await websockets.serve(
        simple_echo_server, 
        args.host, 
        args.port
    )
    
    print(f"WebSocket服务器已启动，监听 {args.host}:{args.port}")
    print("按 Ctrl+C 停止服务器")
    
    # 保持服务器运行
    await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止")
