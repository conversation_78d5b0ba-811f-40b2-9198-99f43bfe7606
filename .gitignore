# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
*~

# Python compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/

# Package manager files
pip-delete-this-directory.txt
pip-selfcheck.json
Poetry.lock
Pipfile.lock

# Logs and databases
*.log
*.sql
*.sqlite
*.db

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Project specific
init_model/
test_local/
RapidASR
export/
MaaS-lib
modelscope
samples
outputs*
emotion2vec*
GPT-SoVITS*
modelscope_models
examples/aishell/llm_asr_nar/*
models
runtime/websocket/third_party/
runtime/onnxruntime

# Build artifacts
*.tar.gz
*.whl
*.o
*.obj
*.a
*.lib
*.dll
*.so
*.dylib
*.pyd
*.exe
*.out
bin/
obj/
target/

# C/C++ compilation
*.d
*.ilk
*.pdb
*.exp
*.manifest

# CMake build directories
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
Makefile
*.cmake

# Model files and training artifacts
*.pt
*.pth
*.onnx
*.pb
*.h5
*.model
*.checkpoint
*.bin

# Third-party downloaded content
models/damo/
models/thuduj12/
._____temp/

# Documentation
docs/_build/

# Temporary debug and test files
*.log
*.tmp
*.temp
*_test
*_debug
*_fix.md
*_summary.md
*_guide.md
*_status.md
*_report.md
*_FINAL*
*_SUMMARY*
*_GUIDE*
*_STATUS*
*_REPORT*
test_*
