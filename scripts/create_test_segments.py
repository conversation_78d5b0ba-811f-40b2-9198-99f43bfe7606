#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create test audio segments for C++ testing.
"""
import argparse
import soundfile as sf
import numpy as np
from pathlib import Path

def create_segment(input_wav: str, output_wav: str, start_time: float, duration: float):
    """Create a segment from the input wav file."""
    wav, fs = sf.read(input_wav, dtype='float32')
    if wav.ndim == 2:
        wav = wav[:, 0]  # Take first channel if stereo
    
    start_sample = int(start_time * fs)
    end_sample = int((start_time + duration) * fs)
    segment = wav[start_sample:end_sample]
    
    sf.write(output_wav, segment, fs)
    print(f"Created {output_wav}: {start_time:.1f}s-{start_time+duration:.1f}s ({len(segment)/fs:.2f}s)")

def main():
    parser = argparse.ArgumentParser(description="Create test audio segments")
    parser.add_argument('--input', type=str, default='./sound.wav', help='Input wav file')
    parser.add_argument('--output-dir', type=str, default='./test_segments', help='Output directory')
    args = parser.parse_args()
    
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"Input file not found: {input_path}")
        return
    
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Create test segments
    segments = [
        ("first_6s.wav", 0.0, 6.0),      # Same speaker
        ("middle_6s.wav", 12.0, 6.0),   # Different speaker
        ("last_6s.wav", 25.4, 6.0),     # Another speaker
    ]
    
    for filename, start, duration in segments:
        output_path = output_dir / filename
        create_segment(str(input_path), str(output_path), start, duration)
    
    print(f"\nTest segments created in {output_dir}")
    print("You can now test these with C++ implementation:")
    for filename, start, duration in segments:
        print(f"  {filename}: {start:.1f}s-{start+duration:.1f}s")

if __name__ == '__main__':
    main()
