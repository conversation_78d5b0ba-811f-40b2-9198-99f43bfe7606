#!/usr/bin/env python3
import numpy as np
import librosa
import onnxruntime as ort
import kaldi_native_fbank as knf
import torch
from torchaudio.compliance.kaldi import fbank as kaldi_fbank
import sys
import os
from pathlib import Path

def extract_fbank_python_style(audio, sample_rate=16000):
    """Extract fbank features using torchaudio.compliance.kaldi (Python FunASR style)"""
    # Convert to torch tensor
    wav_tensor = torch.from_numpy(audio).float().unsqueeze(0)

    # Use torchaudio.compliance.kaldi.fbank exactly like Python FunASR
    feats = kaldi_fbank(wav_tensor, num_mel_bins=80)  # (T, 80)

    # Apply mean normalization like Python FunASR
    feats = feats - feats.mean(dim=0, keepdim=True)

    return feats.numpy().astype(np.float32)

def extract_fbank_kaldi_style(audio, sample_rate=16000):
    """Extract fbank features using kaldi-native-fbank (C++ style)"""
    # Match C++ parameters exactly
    opts = knf.FbankOptions()
    opts.frame_opts.dither = 0.0
    opts.frame_opts.snip_edges = True
    opts.frame_opts.samp_freq = float(sample_rate)
    opts.frame_opts.frame_shift_ms = 10.0
    opts.frame_opts.frame_length_ms = 25.0
    opts.mel_opts.num_bins = 80
    opts.use_log_fbank = True
    opts.use_energy = False

    fbank = knf.OnlineFbank(opts)

    # DO NOT scale audio - use raw float values like updated C++
    # Accept waveform
    fbank.accept_waveform(sample_rate, audio.tolist())
    fbank.input_finished()

    # Extract features
    features = []
    for i in range(fbank.num_frames_ready):
        frame = fbank.get_frame(i)
        features.append(frame)

    if not features:
        return np.array([]).reshape(0, 80)

    features = np.array(features)

    # CRITICAL: Apply mean normalization like Python version and updated C++
    # Python: feats = feats - feats.mean(dim=0, keepdim=True)
    if features.shape[0] > 0:
        features = features - features.mean(axis=0, keepdims=True)

    return features

def run_onnx_inference(model_path, features):
    """Run ONNX inference and return embedding"""
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Prepare input
    if features.shape[0] == 0:
        return np.zeros((0,), dtype=np.float32)
    
    # Add batch dimension: (T, 80) -> (1, T, 80)
    input_data = features[None, ...].astype(np.float32)
    
    # Get input name
    input_name = session.get_inputs()[0].name
    
    # Run inference
    outputs = session.run(None, {input_name: input_data})
    
    # Extract embedding (assume first output)
    embedding = outputs[0].squeeze()
    if embedding.ndim > 1:
        embedding = embedding.reshape(-1)
    
    # L2 normalize
    norm = np.linalg.norm(embedding) + 1e-12
    embedding = embedding / norm
    
    return embedding.astype(np.float32)

def main():
    if len(sys.argv) != 3:
        print("Usage: python debug_campplus_vectors.py <wav_file> <model_dir>")
        sys.exit(1)
    
    wav_path = sys.argv[1]
    model_dir = sys.argv[2]
    model_path = os.path.join(model_dir, "model.onnx")
    
    if not os.path.exists(wav_path):
        print(f"WAV file not found: {wav_path}")
        sys.exit(1)
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        sys.exit(1)
    
    print(f"=== Debugging CAMP++ Vector Extraction ===")
    print(f"WAV file: {wav_path}")
    print(f"Model: {model_path}")
    
    # Load audio
    audio, sr = librosa.load(wav_path, sr=16000)
    print(f"Audio loaded: {len(audio)} samples, {len(audio)/sr:.2f}s")
    
    # Extract features using both methods
    print("\n=== Feature Extraction Comparison ===")
    
    # Python style (librosa)
    features_python = extract_fbank_python_style(audio, sr)
    print(f"Python-style features: {features_python.shape}")
    print(f"  Mean: {features_python.mean():.6f}, Std: {features_python.std():.6f}")
    print(f"  Min: {features_python.min():.6f}, Max: {features_python.max():.6f}")
    
    # Kaldi style (matching C++)
    features_kaldi = extract_fbank_kaldi_style(audio, sr)
    print(f"Kaldi-style features: {features_kaldi.shape}")
    print(f"  Mean: {features_kaldi.mean():.6f}, Std: {features_kaldi.std():.6f}")
    print(f"  Min: {features_kaldi.min():.6f}, Max: {features_kaldi.max():.6f}")
    
    # Run ONNX inference with both feature sets
    print("\n=== ONNX Inference Comparison ===")
    
    if features_python.shape[0] > 0:
        embedding_python = run_onnx_inference(model_path, features_python)
        print(f"Python-style embedding: shape={embedding_python.shape}, norm={np.linalg.norm(embedding_python):.6f}")
        print(f"  First 10 values: {embedding_python[:10]}")
    else:
        print("Python-style features empty, skipping inference")
        embedding_python = None
    
    if features_kaldi.shape[0] > 0:
        embedding_kaldi = run_onnx_inference(model_path, features_kaldi)
        print(f"Kaldi-style embedding: shape={embedding_kaldi.shape}, norm={np.linalg.norm(embedding_kaldi):.6f}")
        print(f"  First 10 values: {embedding_kaldi[:10]}")
    else:
        print("Kaldi-style features empty, skipping inference")
        embedding_kaldi = None
    
    # Compare embeddings
    if embedding_python is not None and embedding_kaldi is not None:
        print("\n=== Embedding Comparison ===")
        cosine_sim = np.dot(embedding_python, embedding_kaldi)
        print(f"Cosine similarity between Python and Kaldi embeddings: {cosine_sim:.6f}")
        
        mse = np.mean((embedding_python - embedding_kaldi) ** 2)
        print(f"MSE between embeddings: {mse:.6f}")
        
        if cosine_sim > 0.9:
            print("✅ Embeddings are very similar - feature extraction is consistent")
        elif cosine_sim > 0.7:
            print("⚠️  Embeddings are somewhat similar - minor differences in feature extraction")
        else:
            print("❌ Embeddings are very different - major issue in feature extraction")
    
    print("\n=== Analysis Complete ===")

if __name__ == "__main__":
    main()
