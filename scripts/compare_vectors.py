#!/usr/bin/env python3
import numpy as np
import librosa
import torch
from funasr import AutoModel
import sys
import os

def load_audio_segment(wav_path, start_time=0.0, duration=3.0):
    """Load audio segment for embedding extraction"""
    audio, sr = librosa.load(wav_path, sr=16000)

    start_sample = int(start_time * sr)
    end_sample = int((start_time + duration) * sr)

    if end_sample > len(audio):
        end_sample = len(audio)

    segment = audio[start_sample:end_sample]

    # Pad or trim to exact duration
    target_samples = int(duration * sr)
    if len(segment) < target_samples:
        segment = np.pad(segment, (0, target_samples - len(segment)))
    else:
        segment = segment[:target_samples]

    return segment

def extract_python_embeddings(wav_path, model_dir):
    """Extract embeddings using Python FunASR"""
    print(f"Loading Python model from: {model_dir}")
    model = AutoModel(
        model="damo/speech_campplus_sv_zh-cn_16k-common",
        model_revision="v2.0.2",
        device="cpu"
    )
    
    # Extract embedding for the entire audio file
    audio, sr = librosa.load(wav_path, sr=16000)
    print(f"Loaded audio: {len(audio)} samples, {len(audio)/sr:.2f}s")

    # Convert to the format expected by the model
    audio_tensor = torch.from_numpy(audio).float().unsqueeze(0)

    # Extract embedding
    embeddings = []
    with torch.no_grad():
        result = model.generate(audio_tensor, cache={})
        if 'spk_embedding' in result[0]:
            embedding = result[0]['spk_embedding']
            if isinstance(embedding, torch.Tensor):
                embedding = embedding.cpu().numpy()
            embeddings.append(embedding.flatten())
            print(f"Python embedding shape: {embedding.shape}, norm: {np.linalg.norm(embedding):.6f}")
            print(f"First 10 values: {embedding.flatten()[:10]}")
        else:
            print(f"No embedding found in result: {result[0].keys()}")
            embeddings.append(None)
    
    return embeddings

def main():
    if len(sys.argv) != 3:
        print("Usage: python compare_vectors.py <wav_file> <model_dir>")
        sys.exit(1)
    
    wav_path = sys.argv[1]
    model_dir = sys.argv[2]
    
    if not os.path.exists(wav_path):
        print(f"WAV file not found: {wav_path}")
        sys.exit(1)
    
    if not os.path.exists(model_dir):
        print(f"Model directory not found: {model_dir}")
        sys.exit(1)
    
    print("=== Extracting Python embeddings ===")
    python_embeddings = extract_python_embeddings(wav_path, model_dir)
    
    print("\n=== Python Results Summary ===")
    if python_embeddings and python_embeddings[0] is not None:
        emb = python_embeddings[0]
        print(f"Embedding shape: {emb.shape}, norm: {np.linalg.norm(emb):.6f}")
        print(f"Full embedding values:")
        print(emb)
    else:
        print("Failed to extract embedding")

if __name__ == "__main__":
    main()
