#!/usr/bin/env python3
import argparse
import numpy as np
import soundfile as sf
import torch
from torchaudio.compliance.kaldi import fbank as kaldi_fbank
from pathlib import Path

parser = argparse.ArgumentParser()
parser.add_argument('--wav', required=True)
parser.add_argument('--start', type=float, default=0.0)
parser.add_argument('--dur', type=float, default=1.5)
parser.add_argument('--dump', type=str, default='./tmp/camp_fbank_cpp.bin')
args = parser.parse_args()

# Prefer using exact C++ buf if available
buf_path = Path('./tmp/camp_buf_cpp.bin')
if buf_path.exists():
    with open(buf_path, 'rb') as f:
        header = np.frombuffer(f.read(8), dtype=np.int32)
        n, c = int(header[0]), int(header[1])
        wav = np.frombuffer(f.read(), dtype=np.float32)
    fs = 16000
else:
    # Load segment and resample to 16k (float32 in -1..1)
    wav, fs = sf.read(args.wav, dtype='float32')
    if wav.ndim == 2:
        wav = wav[:,0]
    if fs != 16000:
        import torchaudio
        wav_t = torch.from_numpy(wav).unsqueeze(0)
        wav = torchaudio.transforms.Resample(fs, 16000)(wav_t).squeeze(0).numpy()
        fs = 16000
    start = int(args.start * fs)
    end = int((args.start + args.dur) * fs)
    wav = wav[start:end]

# If we loaded exact C++ buf, use it directly; otherwise mimic C API int16 path
if buf_path.exists():
    wav_cmp = wav.astype(np.float32)
else:
    wav_q = np.clip(wav, -1.0, 1.0)
    wav_q = np.round(wav_q * 32767.0).astype(np.int16)
    wav_cmp = (wav_q.astype(np.float32) / 32768.0)

# Python fbank raw and CMN
wav_t = torch.from_numpy(wav_cmp).float().unsqueeze(0)
py_raw = kaldi_fbank(wav_t, num_mel_bins=80, dither=0.0, snip_edges=True, sample_frequency=16000, use_energy=False, window_type='povey', low_freq=20.0, high_freq=0.0, htk_compat=False)  # (T,80)
py = py_raw - py_raw.mean(dim=0, keepdim=True)
py_raw = py_raw.numpy().astype(np.float32)
py = py.numpy().astype(np.float32)

# Read C++ dumps
with open(args.dump, 'rb') as f:
    header = np.frombuffer(f.read(8), dtype=np.int32)
    T, D = int(header[0]), int(header[1])
    data = np.frombuffer(f.read(), dtype=np.float32)
    cpp = data.reshape(T, D)
try:
    with open(args.dump.replace('.bin', '_raw.bin'), 'rb') as f:
        header = np.frombuffer(f.read(8), dtype=np.int32)
        T0, D0 = int(header[0]), int(header[1])
        data = np.frombuffer(f.read(), dtype=np.float32)
        cpp_raw = data.reshape(T0, D0)
except FileNotFoundError:
    cpp_raw = None

print(f'Python fbank: {py.shape}, C++ fbank: {cpp.shape}')
T2 = min(py.shape[0], cpp.shape[0])
py2 = py[:T2]
cpp2 = cpp[:T2]

mse = ((py2 - cpp2)**2).mean()
mean_diff = (py2.mean(axis=0) - cpp2.mean(axis=0)).mean()
print(f'MSE={mse:.6e}, mean_diff(avg over dims)={mean_diff:.6e}')

# Raw compare
if cpp_raw is not None:
    Tr = min(py_raw.shape[0], cpp_raw.shape[0])
    py_raw2 = py_raw[:Tr]
    cpp_raw2 = cpp_raw[:Tr]
    mse_raw = ((py_raw2 - cpp_raw2)**2).mean()
    print(f'RAW_MSE={mse_raw:.6e}')

# Cosine between column means (rough check)
py_mean = py2.mean(axis=0)
cpp_mean = cpp2.mean(axis=0)
cos = float(py_mean @ cpp_mean / (np.linalg.norm(py_mean)*np.linalg.norm(cpp_mean) + 1e-12))
print(f'Cosine(col-mean)={cos:.6f}')

# Optional: print a few dims
for d in range(0, 80, 16):
    pe = py2[:5, d]
    ce = cpp2[:5, d]
    print(f'dim {d}: py[0:5]={np.round(pe,3)}, cpp[0:5]={np.round(ce,3)}')

