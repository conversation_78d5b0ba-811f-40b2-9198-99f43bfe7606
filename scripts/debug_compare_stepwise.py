#!/usr/bin/env python3
import argparse, struct, numpy as np, soundfile as sf, torch
from torchaudio.compliance.kaldi import fbank as kaldi_fbank

p=argparse.ArgumentParser()
p.add_argument('--wav', required=True)
p.add_argument('--start', type=float, default=0.0)
p.add_argument('--dur', type=float, default=1.5)
args=p.parse_args()

wav,fs=sf.read(args.wav, dtype='float32')
if wav.ndim==2: wav=wav[:,0]
if fs!=16000:
    import torchaudio
    wav=torch.from_numpy(wav).unsqueeze(0)
    wav=torchaudio.transforms.Resample(fs,16000)(wav).squeeze(0).numpy()
    fs=16000
start=int(args.start*fs); end=int((args.start+args.dur)*fs)
wav=wav[start:end]
wav_t=torch.from_numpy(wav).float().unsqueeze(0)

kwargs=dict(num_mel_bins=80, dither=0.0, snip_edges=True, sample_frequency=16000, use_energy=False, window_type='povey', low_freq=20.0, high_freq=0.0, htk_compat=False)
py=kaldi_fbank(wav_t, **kwargs)
py=py.numpy()

# read cpp dumps for frames 0..2
import os
base='./tmp'
paths={'raw': f'{base}/camp_fbank_cpp_raw.bin'}
for t in range(3):
    paths[f'win{t}']=f'{base}/camp_window_cpp_f{t}.bin'
    paths[f'spec{t}']=f'{base}/camp_spec_cpp_f{t}.bin'
    paths[f'mel{t}']=f'{base}/camp_mel_linear_cpp_f{t}.bin'
for k,v in paths.items():
    if not os.path.exists(v):
        print('missing', v)
        raise SystemExit(1)

def load_vec(p):
    with open(p,'rb') as f:
        T,D=struct.unpack('ii', f.read(8))
        dat=np.frombuffer(f.read(), dtype=np.float32)
    return dat

from torchaudio.compliance.kaldi import _get_waveform_and_window_properties, _get_window, get_mel_banks
waveform, window_shift, window_size, padded_window_size = _get_waveform_and_window_properties(
    wav_t, -1, 16000.0, 10.0, 25.0, True, 0.97)
strided, _ = _get_window(waveform, padded_window_size, window_size, window_shift,
                         'povey', 0.42, True, True, 1.0, 0.0, True, 0.97)
import torch
mel_energies, _ = get_mel_banks(80, padded_window_size, 16000.0, 20.0, 0.0, 100.0, -500.0, 1.0)
mel_energies = torch.nn.functional.pad(mel_energies, (0,1), mode='constant', value=0)

with open(paths['raw'], 'rb') as f:
    Traw, Draw = struct.unpack('ii', f.read(8))
    cpp_raw = np.frombuffer(f.read(), dtype=np.float32).reshape(Traw, Draw)

for t in range(3):
    win=load_vec(paths[f'win{t}'])
    spec=load_vec(paths[f'spec{t}'])
    mel_lin_cpp=load_vec(paths[f'mel{t}'])
    spectrum = torch.fft.rfft(strided[t:t+1]).abs()
    spectrum = spectrum.pow(2.0)
    mel_python = spectrum @ mel_energies.T
    mse_lin = float(((mel_python[0].numpy()-mel_lin_cpp)**2).mean())
    eps_same = 1.1920928955078125e-07
    mel_log_cpp = np.log(np.maximum(mel_lin_cpp, eps_same))
    py_from_lin = torch.log(torch.clamp(mel_python, min=eps_same))[0].numpy()
    mse_py_recompose = float(((py_from_lin - py[t])**2).mean())
    mse_log = float(((py[t]-mel_log_cpp)**2).mean())
    mse_row = float(((py[t]-cpp_raw[t])**2).mean())
    print(f'frame {t}: MSE lin={mse_lin:.3e}, MSE py(recompose)={mse_py_recompose:.3e}, MSE log={mse_log:.3e}, MSE rawRow={mse_row:.3e}')

# full sweep over all frames
T = min(strided.shape[0], cpp_raw.shape[0], py.shape[0])
spectrum_all = torch.fft.rfft(strided[:T]).abs().pow(2.0)
mel_python_all = spectrum_all @ mel_energies.T
py_from_lin_all = torch.log(torch.clamp(mel_python_all, min=eps_same)).numpy()
print('ALL: MSE py_from_lin_all vs py    =', float(((py_from_lin_all - py[:T])**2).mean()))
print('ALL: MSE py_from_lin_all vs cppRaw=', float(((py_from_lin_all - cpp_raw[:T])**2).mean()))
print('ALL: MSE py vs cppRaw              =', float(((py[:T] - cpp_raw[:T])**2).mean()))

