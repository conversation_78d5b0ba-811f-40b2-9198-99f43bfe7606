#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compare Python and C++ CAMP++ implementations for consistency.
"""
import argparse
import sys
import numpy as np
from pathlib import Path
import subprocess
import tempfile
import os

try:
    import soundfile as sf
except Exception as e:
    print("Please install soundfile: pip install soundfile", file=sys.stderr)
    raise

try:
    import torchaudio
    import torch
    from torchaudio.compliance.kaldi import fbank as kaldi_fbank
except Exception as e:
    print("Please install torch/torchaudio: pip install torch torchaudio", file=sys.stderr)
    raise

try:
    import onnxruntime as ort
except Exception as e:
    print("Please install onnxruntime: pip install onnxruntime", file=sys.stderr)
    raise


def load_wav_segment(path: str, start_time: float, duration: float, target_fs: int = 16000) -> np.ndarray:
    wav, fs = sf.read(path, dtype='float32')
    if wav.ndim == 2:
        wav = wav[:, 0]
    # resample if needed
    if fs != target_fs:
        wav_t = torch.from_numpy(wav).unsqueeze(0)
        resampler = torchaudio.transforms.Resample(fs, target_fs)
        wav = resampler(wav_t).squeeze(0).numpy()
        fs = target_fs

    start_sample = int(start_time * fs)
    end_sample = int((start_time + duration) * fs)
    return wav[start_sample:end_sample]


def extract_fbank(wav: np.ndarray, fs: int = 16000) -> np.ndarray:
    """Kaldi fbank 80-dim, 25ms/10ms, matching Python CAMPPlus exactly."""
    if len(wav) == 0:
        return np.zeros((0, 80), dtype=np.float32)
    wav_t = torch.from_numpy(wav).float().unsqueeze(0)
    # Match Python CAMPPlus exactly: Kaldi.fbank(au.unsqueeze(0), num_mel_bins=80)
    # All other params use torchaudio defaults
    feats = kaldi_fbank(wav_t, num_mel_bins=80)  # (T, 80)
    feats = feats - feats.mean(dim=0, keepdim=True)
    return feats.numpy().astype(np.float32)


def sliding_windows(total_len_samples: int, fs: int, seg_dur: float, seg_shift: float):
    seg_len = int(seg_dur * fs)
    step = int(seg_shift * fs)
    starts = list(range(0, max(0, total_len_samples - seg_len + 1), step))
    # keep only full windows
    return [(s, s + seg_len) for s in starts if s + seg_len <= total_len_samples]


def pick_embedding_output_idx(session: ort.InferenceSession) -> int:
    # Prefer output named 'embedding' or containing 'emb'
    outs = session.get_outputs()
    for i, o in enumerate(outs):
        name = (o.name or '').lower()
        if name == 'embedding' or 'emb' in name:
            return i
    return 0


def run_onnx_embedding(session: ort.InferenceSession, feats: np.ndarray, out_idx: int) -> np.ndarray:
    # feats: (T, 80) -> input (1, T, 80)
    if feats.shape[0] == 0:
        return np.zeros((0,), dtype=np.float32)
    inp = feats[None, ...].astype(np.float32)
    # find first input name
    in_name = session.get_inputs()[0].name
    outs = session.run(None, {in_name: inp})
    emb = outs[out_idx].squeeze()
    if emb.ndim > 1:
        # Try last dim as embedding dimension
        emb = emb.reshape(-1)[-emb.shape[-1]:]
    # L2 normalize
    norm = np.linalg.norm(emb) + 1e-12
    emb = emb / norm
    return emb.astype(np.float32)


def cosine_sim_matrix(embs: np.ndarray) -> np.ndarray:
    # embs: (N, D) assumed L2-normalized
    if embs.shape[0] == 0:
        return np.zeros((0, 0), dtype=np.float32)
    sims = embs @ embs.T
    return sims


def run_python_campplus(wav_path: str, model_dir: str, start_time: float = 0.0,
                       duration: float = 6.0, seg_dur: float = 1.5, seg_shift: float = 0.75):
    """Run Python CAMP++ implementation."""
    print("=== Python CAMP++ Implementation ===")

    wav = load_wav_segment(wav_path, start_time, duration, 16000)
    print(f"Waveform: {start_time:.1f}s-{start_time+duration:.1f}s, {len(wav)/16000:.2f}s, {len(wav)} samples at 16000 Hz")

    # Prepare sliding windows
    windows = sliding_windows(len(wav), 16000, seg_dur, seg_shift)
    print(f"Windows (dur={seg_dur}s, shift={seg_shift}s): {len(windows)}")

    # Init ONNX session
    model_path = Path(model_dir) / 'model.onnx'
    sess = ort.InferenceSession(str(model_path), providers=['CPUExecutionProvider'])
    out_idx = pick_embedding_output_idx(sess)

    embs = []
    for wi, (s, e) in enumerate(windows):
        seg = wav[s:e]
        feats = extract_fbank(seg, 16000)
        emb = run_onnx_embedding(sess, feats, out_idx)
        if emb.size == 0:
            print(f"[WARN] Empty embedding for window {wi} [{s},{e}]", file=sys.stderr)
        embs.append(emb)
        print(f"  Window {wi}: embedding shape={emb.shape}, norm={np.linalg.norm(emb):.6f}")
    
    embs = np.stack(embs)
    sims = cosine_sim_matrix(embs)
    
    print("Similarity matrix:")
    with np.printoptions(precision=3, suppress=True):
        print(sims)
    
    return embs, sims


def run_cpp_campplus(wav_path: str, model_dir: str, cpp_executable: str):
    """Run C++ CAMP++ implementation."""
    print("\n=== C++ CAMP++ Implementation ===")

    try:
        # Set up environment for C++ executable
        env = os.environ.copy()
        cpp_path = Path(cpp_executable)
        lib_dir = cpp_path.parent / 'src'
        if lib_dir.exists():
            env['DYLD_LIBRARY_PATH'] = f"{lib_dir}:{env.get('DYLD_LIBRARY_PATH', '')}"

        # Run the C++ executable
        result = subprocess.run([cpp_executable, wav_path, model_dir],
                              capture_output=True, text=True, timeout=60, env=env)

        if result.returncode != 0:
            print(f"C++ executable failed with return code {result.returncode}")
            print(f"stderr: {result.stderr}")
            return None

        print("C++ output:")
        print(result.stdout)

        # Parse the output to extract speaker information
        lines = result.stdout.split('\n')
        speakers = []
        for line in lines:
            if 'speaker_id=' in line:
                # Extract speaker ID from line like "  Segment 0 (t=0.0s): speaker_id=0"
                parts = line.split('speaker_id=')
                if len(parts) > 1:
                    speaker_id = int(parts[1].strip())
                    speakers.append(speaker_id)

        return speakers

    except subprocess.TimeoutExpired:
        print("C++ executable timed out")
        return None
    except Exception as e:
        print(f"Error running C++ executable: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="Compare Python and C++ CAMP++ implementations")
    parser.add_argument('--wav', type=str, default='./sound.wav', help='Path to wav file')
    parser.add_argument('--model-dir', type=str, required=True, help='Directory containing model.onnx for CAMP++')
    parser.add_argument('--cpp-exe', type=str,
                       default='runtime/websocket/build/test_campplus_simple',
                       help='Path to C++ executable')
    parser.add_argument('--start-time', type=float, default=0.0, help='Start time (seconds)')
    parser.add_argument('--duration', type=float, default=6.0, help='Duration to test (seconds)')
    parser.add_argument('--seg-dur', type=float, default=1.5, help='Segment duration (seconds)')
    parser.add_argument('--seg-shift', type=float, default=0.75, help='Segment shift (seconds)')
    args = parser.parse_args()

    wav_path = Path(args.wav)
    if not wav_path.exists():
        print(f"Wav not found: {wav_path}", file=sys.stderr)
        sys.exit(1)

    model_path = Path(args.model_dir) / 'model.onnx'
    if not model_path.exists():
        print(f"ONNX model not found: {model_path}", file=sys.stderr)
        sys.exit(1)

    cpp_exe = Path(args.cpp_exe)
    if not cpp_exe.exists():
        print(f"C++ executable not found: {cpp_exe}", file=sys.stderr)
        sys.exit(1)

    print(f"Comparing Python vs C++ CAMP++ implementations")
    print(f"WAV file: {wav_path}")
    print(f"Model dir: {args.model_dir}")
    print(f"C++ executable: {cpp_exe}")

    # Run Python implementation
    python_embs, python_sims = run_python_campplus(
        str(wav_path), args.model_dir, args.start_time, args.duration, args.seg_dur, args.seg_shift)

    # Run C++ implementation
    cpp_speakers = run_cpp_campplus(str(wav_path), args.model_dir, str(cpp_exe))

    # Compare results
    print("\n=== Comparison Results ===")
    
    if cpp_speakers is not None:
        print(f"Python: {len(python_embs)} embeddings generated")
        print(f"C++: {len(cpp_speakers)} speaker assignments")
        
        if len(python_embs) == len(cpp_speakers):
            print("✓ Same number of segments processed")
        else:
            print("✗ Different number of segments processed")
        
        print(f"C++ speaker assignments: {cpp_speakers}")
        
        # Analyze Python similarity patterns
        tri = python_sims[np.triu_indices_from(python_sims, k=1)]
        if tri.size > 0:
            print(f"Python similarity stats: min={tri.min():.4f}, mean={tri.mean():.4f}, max={tri.max():.4f}")
            
            # Check if Python results suggest single speaker (high similarities)
            if tri.mean() > 0.8:
                print("Python suggests: Single speaker (high similarities)")
            elif tri.mean() > 0.6:
                print("Python suggests: Possibly single speaker (moderate similarities)")
            else:
                print("Python suggests: Multiple speakers (low similarities)")
        
        # Check C++ speaker diversity
        unique_speakers = len(set(cpp_speakers))
        print(f"C++ detected {unique_speakers} unique speakers")
        
        if unique_speakers == 1:
            print("C++ suggests: Single speaker")
        else:
            print(f"C++ suggests: Multiple speakers ({unique_speakers} speakers)")
    else:
        print("✗ C++ implementation failed to run")

    print("\nComparison completed!")


if __name__ == '__main__':
    main()
