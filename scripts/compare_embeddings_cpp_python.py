#!/usr/bin/env python3
import argparse
import numpy as np
import soundfile as sf
import torch
import torchaudio
import onnxruntime as ort
from torchaudio.compliance.kaldi import fbank as kaldi_fbank
from pathlib import Path

parser = argparse.ArgumentParser()
parser.add_argument('--wav', required=True)
parser.add_argument('--model-dir', required=True)
parser.add_argument('--start', type=float, default=0.0)
parser.add_argument('--dur', type=float, default=1.5)
parser.add_argument('--dump', type=str, default='./tmp/camp_fbank_cpp.bin')
args = parser.parse_args()

# Prefer exact C++ buf if available to guarantee identical framing
buf_path = Path('./tmp/camp_buf_cpp.bin')
if buf_path.exists():
    with open(buf_path, 'rb') as f:
        T0, D0 = np.frombuffer(f.read(8), dtype=np.int32)
        wav = np.frombuffer(f.read(), dtype=np.float32)
    fs = 16000
else:
    # Load segment and resample to 16k
    wav, fs = sf.read(args.wav, dtype='float32')
    if wav.ndim == 2:
        wav = wav[:,0]
    if fs != 16000:
        wav_t = torch.from_numpy(wav).unsqueeze(0)
        wav = torchaudio.transforms.Resample(fs, 16000)(wav_t).squeeze(0).numpy()
        fs = 16000
    start = int(args.start * fs)
    end = int((args.start + args.dur) * fs)
    wav = wav[start:end]

# Python fbank (use exactly same opts as C++)
wav_t = torch.from_numpy(wav).float().unsqueeze(0)
py = kaldi_fbank(wav_t, num_mel_bins=80, dither=0.0, snip_edges=True, sample_frequency=16000, use_energy=False, window_type='povey', low_freq=20.0, high_freq=0.0, htk_compat=False)
py = py - py.mean(dim=0, keepdim=True)
py = py.numpy().astype(np.float32)

# C++ fbank (dumped)
with open(args.dump, 'rb') as f:
    header = np.frombuffer(f.read(8), dtype=np.int32)
    T, D = int(header[0]), int(header[1])
    data = np.frombuffer(f.read(), dtype=np.float32)
    cpp = data.reshape(T, D)

T2 = min(py.shape[0], cpp.shape[0])
py = py[:T2]
cpp = cpp[:T2]

sess = ort.InferenceSession(str(Path(args.model_dir) / 'model.onnx'), providers=['CPUExecutionProvider'])
in_name = sess.get_inputs()[0].name

def run(feats):
    inp = feats[None, ...].astype(np.float32)
    out = sess.run(None, {in_name: inp})[0]
    emb = out.reshape(-1)[-out.shape[-1]:].astype(np.float32)
    n = np.linalg.norm(emb) + 1e-12
    return emb / n

emb_py = run(py)
emb_cpp = run(cpp)

cos = float(emb_py @ emb_cpp)
print('Embedding cosine(py vs cpp)=', cos)
print('emb_py[:8]=', np.round(emb_py[:8], 4))
print('emb_cpp[:8]=', np.round(emb_cpp[:8], 4))

