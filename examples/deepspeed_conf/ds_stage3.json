{"train_micro_batch_size_per_gpu": 1, "gradient_accumulation_steps": 1, "steps_per_print": 100, "gradient_clipping": 5, "fp16": {"enabled": false, "auto_cast": false, "loss_scale": 0, "initial_scale_power": 16, "loss_scale_window": 1000, "hysteresis": 2, "consecutive_hysteresis": false, "min_loss_scale": 1}, "bf16": {"enabled": true}, "zero_force_ds_cpu_optimizer": false, "zero_optimization": {"stage": 3, "offload_optimizer": {"device": "none", "pin_memory": true}, "offload_param": {"device": "none", "pin_memory": true}, "allgather_partitions": true, "allgather_bucket_size": 500000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 500000000.0, "contiguous_gradients": true, "stage3_max_live_parameters": 1000000000.0, "stage3_max_reuse_distance": 1000000000.0, "stage3_prefetch_bucket_size": 500000000.0, "stage3_param_persistence_threshold": 100000.0}}