# OnlineSpeakerClustering::AssignSpeaker 详细流程图

## 整体流程概览

```mermaid
graph TD
    A[开始: AssignSpeaker调用] --> B{嵌入是否为空?}
    B -->|是| C[返回-1: 无语音活动]
    B -->|否| D[计算与所有聚类的相似度]
    D --> E{是否存在超过阈值的聚类?}
    E -->|是| F[情况1: 分配到现有聚类]
    E -->|否| G[情况2: 无匹配聚类处理]
    F --> H[更新聚类中心]
    G --> I{是否有现有聚类?}
    I -->|否| J[子情况2.1: 首次聚类创建]
    I -->|是| K[子情况2.2: 相似度不足处理]
    J --> L{累积时长是否足够?}
    L -->|否| M[延迟分配, 返回-2]
    L -->|是| N[创建新聚类]
    K --> O{是否为短片段且相似度很低?}
    O -->|是| P[延迟分配, 返回-2]
    O -->|否| Q[分配到最相似聚类或创建新聚类]
    N --> R[添加到片段缓冲区]
    Q --> R
    H --> R
    M --> S[存储延迟片段]
    P --> S
    R --> T{缓冲区大小检查}
    T -->|每50个片段| U[执行聚类合并]
    T -->|正常| V[返回说话人ID]
    U --> V
    S --> W[返回-2]
```

## 详细分支流程

### 情况1: 有匹配聚类 (cluster_id >= 0)

```mermaid
graph TD
    A1[找到匹配聚类] --> B1{相似度是否超过更新阈值?}
    B1 -->|是| C1[更新聚类中心<br/>使用指数移动平均]
    B1 -->|否| D1[仅分配不更新]
    C1 --> E1[添加到片段缓冲区]
    D1 --> E1
```

### 情况2: 无匹配聚类 (cluster_id == -1)

#### 子情况2.1: 无任何聚类存在

```mermaid
graph TD
    A2[首次处理片段] --> B2[计算累积时长<br/>当前+延迟片段]
    B2 --> C2{累积时长≥2.0s?}
    C2 -->|否| D2[延迟分配<br/>存储片段到deferred_segments_]
    C2 -->|是| E2[合并延迟片段创建新聚类]
    D2 --> F2[返回-2]
    E2 --> G2[创建新聚类中心]
    G2 --> H2[更新聚类状态]
    H2 --> I2[返回新聚类ID]
```

#### 子情况2.2: 有聚类但相似度不足

```mermaid
graph TD
    A3[有聚类但相似度不足] --> B3{是否为短片段且相似度很低?}
    B3 -->|是| C3[延迟分配<br/>存储片段到deferred_segments_]
    B3 -->|否| D3{相似度是否达到分配阈值?}
    C3 --> E3[返回-2]
    D3 -->|是| F3[分配到最相似聚类]
    D3 -->|否| G3[创建新聚类]
    F3 --> H3[处理延迟片段合并]
    H3 --> I3[更新聚类中心]
    G3 --> J3[检查最大说话人数限制]
    J3 --> K3{是否达到上限?}
    K3 -->|否| L3[创建新聚类]
    K3 -->|是| M3[返回-1: 未知说话人]
    L3 --> N3[返回新聚类ID]
```

## 关键算法流程图

### 相似度计算流程

```mermaid
graph TD
    A4[开始计算相似度] --> B4{嵌入尺寸是否匹配?}
    B4 -->|否| C4[返回0.0]
    B4 -->|是| D4[计算点积和范数]
    D4 --> E4{范数是否接近0?}
    E4 -->|是| F4[返回0.0]
    E4 -->|否| G4[计算余弦相似度]
    G4 --> H4[返回相似度值]
```

### 聚类更新流程

```mermaid
graph TD
    A5[开始更新聚类] --> B5{聚类ID是否有效?}
    B5 -->|否| C5[直接返回]
    B5 -->|是| D5[计算学习率alpha]
    D5 --> E5[应用指数移动平均]
    E5 --> F5[更新聚类计数]
    F5 --> G5[完成更新]
```

### 聚类合并流程

```mermaid
graph TD
    A6[开始聚类合并] --> B6{聚类数量≥2?}
    B6 -->|否| C6[直接返回]
    B6 -->|是| D6[遍历所有聚类对]
    D6 --> E6[计算聚类间相似度]
    E6 --> F6{相似度>合并阈值?}
    F6 -->|否| G6[继续下一对]
    F6 -->|是| H6[合并聚类j到i]
    H6 --> I6[更新聚类状态]
    I6 --> J6[重新分配片段ID]
    J6 --> K6[完成合并]
```

## 数据流图

```mermaid
graph LR
    A[输入嵌入] --> B[相似度计算]
    C[现有聚类中心] --> B
    B --> D[相似度比较]
    D --> E{分配决策}
    E --> F[现有聚类]
    E --> G[新聚类]
    E --> H[延迟分配]
    F --> I[聚类更新]
    G --> J[聚类创建]
    H --> K[片段存储]
    I --> L[输出结果]
    J --> L
    K --> M[等待后续合并]
```

## 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 初始状态
    初始状态 --> 计算相似度
    计算相似度 --> 有匹配聚类: 相似度>阈值
    计算相似度 --> 无匹配聚类: 相似度≤阈值
    
    有匹配聚类 --> 更新聚类: 相似度>更新阈值
    有匹配聚类 --> 仅分配: 相似度≤更新阈值
    更新聚类 --> 输出结果
    仅分配 --> 输出结果
    
    无匹配聚类 --> 首次聚类: 无现有聚类
    无匹配聚类 --> 相似度不足: 有现有聚类
    
    首次聚类 --> 延迟分配: 累积时长<2.0s
    首次聚类 --> 创建聚类: 累积时长≥2.0s
    延迟分配 --> 等待合并
    创建聚类 --> 输出结果
    
    相似度不足 --> 延迟分配: 短片段+低相似度
    相似度不足 --> 分配或创建: 其他情况
    延迟分配 --> 等待合并
    分配或创建 --> 输出结果
    
    等待合并 --> 合并处理: 收到新片段
    合并处理 --> 重新分配: 合并成功
    重新分配 --> 输出结果
    
    输出结果 --> [*]
```

## 关键参数说明

### 阈值参数
- **接受阈值 (similarity_threshold)**: 决定是否接受分配到某个聚类
- **更新阈值 (update_threshold)**: 决定是否更新聚类中心
- **合并阈值 (merge_threshold)**: 决定是否合并相似聚类
- **短片段阈值 (kShortUtteranceDurationSec)**: 2.5秒
- **首次聚类最小时长 (kFirstClusterMinDurationSec)**: 2.0秒

### 动态参数
- **学习率 (alpha)**: 基于聚类样本数量动态调整
- **分配阈值**: 基于接受阈值动态计算
- **短片段分配阈值**: 基于接受阈值动态计算

