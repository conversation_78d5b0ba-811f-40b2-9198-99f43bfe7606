# Deferred 段落合并逻辑修复

## 🐛 问题描述

### 原始问题

从用户提供的日志可以看到：

```
第1段: duration=1.49s, max_sim=0.187901 < 0.35, deferred_segments_count=0
       → 返回 -2，添加到 deferred_segments_

第2段: duration=2.11s, max_sim=0.165772 < 0.35, deferred_segments_count=1
       → 又返回 -2，再次添加到 deferred_segments_
       ❌ 问题：没有触发合并！

第3段: duration=0.705s, max_sim=0.113723 < 0.35, deferred_segments_count=2
       → 又返回 -2，再次添加到 deferred_segments_
       ❌ 问题：仍然没有触发合并！
```

### 根本原因

**旧逻辑的问题**：

```cpp
// 旧逻辑
if (duration_sec <= kShortUtteranceDurationSec && max_sim < kMinSimilarityForShortUtterance) {
    // 无论 deferred_segments_ 是否为空，都直接 defer
    deferred_segments_.push_back(deferred_segment);
    return -2;
}
```

**问题**：
1. 当遇到短且相似度低的段落时，总是返回 -2 并添加到 `deferred_segments_`
2. 合并逻辑只在 `should_assign = true` 时触发（即相似度足够高）
3. **如果连续多个段落都是短且相似度低，它们会一直被 defer，永远不会触发合并！**

### 实际场景

```
段落1: 1.49s, sim=0.19 → defer (正确)
段落2: 2.11s, sim=0.17 → defer (错误！应该合并)
段落3: 0.71s, sim=0.11 → defer (错误！应该合并)

总时长: 1.49 + 2.11 + 0.71 = 4.31s
```

**如果合并这三个段落**，总时长 4.31s，重新计算的 embedding 很可能有足够高的相似度来识别说话人！

## ✅ 解决方案

### 新逻辑

```cpp
if (duration_sec <= kShortUtteranceDurationSec && max_sim < kMinSimilarityForShortUtterance) {
    if (!deferred_segments_.empty()) {
        // ✅ 已经有 deferred 段落，触发合并
        // 1. 合并所有 deferred 段落 + 当前段落的音频
        // 2. 重新计算 embedding
        // 3. 基于合并后的 embedding 进行识别
        // 4. 如果识别成功，更新所有 deferred 段落
        // 5. 如果仍然失败，继续 defer 当前段落
    } else {
        // ✅ 没有 deferred 段落，defer 当前段落
        deferred_segments_.push_back(deferred_segment);
        return -2;
    }
}
```

### 详细流程

#### 情况1：第一个短且相似度低的段落

```
输入: duration=1.49s, max_sim=0.19, deferred_segments_count=0
处理: defer 这个段落
输出: 返回 -2, deferred_segments_count=1
```

#### 情况2：第二个短且相似度低的段落

```
输入: duration=2.11s, max_sim=0.17, deferred_segments_count=1
处理:
  1. 合并音频: deferred[0].audio + current.audio
  2. 总时长: 1.49s + 2.11s = 3.6s
  3. 重新计算 embedding
  4. 评估相似度:
     - 如果 merged_sim >= threshold: 识别成功
       → 更新 deferred[0] 和 current 的 speaker_id
       → 清空 deferred_segments_
       → 返回 speaker_id
     - 如果 merged_sim < threshold: 仍然不够
       → defer 当前段落
       → 返回 -2, deferred_segments_count=2
```

#### 情况3：第三个短且相似度低的段落

```
输入: duration=0.71s, max_sim=0.11, deferred_segments_count=2
处理:
  1. 合并音频: deferred[0].audio + deferred[1].audio + current.audio
  2. 总时长: 1.49s + 2.11s + 0.71s = 4.31s
  3. 重新计算 embedding
  4. 评估相似度:
     - 如果 merged_sim >= threshold: 识别成功
       → 更新所有 deferred 段落和 current 的 speaker_id
       → 清空 deferred_segments_
       → 返回 speaker_id
     - 如果 merged_sim < threshold: 仍然不够
       → defer 当前段落
       → 返回 -2, deferred_segments_count=3
```

## 📊 预期效果

### 修复前

```
段落1: 1.49s, sim=0.19 → -2 (deferred_count=0→1)
段落2: 2.11s, sim=0.17 → -2 (deferred_count=1→2) ❌ 没有合并
段落3: 0.71s, sim=0.11 → -2 (deferred_count=2→3) ❌ 没有合并
...
永远不会触发合并，所有段落都返回 -2
```

### 修复后

```
段落1: 1.49s, sim=0.19 → -2 (deferred_count=0→1)
段落2: 2.11s, sim=0.17 → 触发合并
  合并: 1.49s + 2.11s = 3.6s
  重新计算 embedding
  merged_sim = 0.65 (假设) ✅ 足够高
  → 段落1 和段落2 都识别为 speaker 1
  → 返回 1, deferred_count=2→0
  → 客户端收到更新：段落1 从 -2 更新为 1
```

## 🔍 调试日志

### 新增的日志

修复后会输出详细的合并日志：

```
DEBUG: Short utterance with low similarity - duration=2.11s, max_sim=0.17, kMinSimilarityForShortUtterance=0.35, deferred_segments_count=1
Merging current segment with 1 deferred segments to get better embedding
DEBUG: Added deferred segment [204.0,205.49], duration=1.49s
DEBUG: Merged audio - total_samples=57600, total_duration=3.6s
DEBUG: ComputeEmbeddingFromAudio - audio_samples=57600, duration=3.6s, sample_rate=16000Hz
DEBUG: Merged embedding similarity - closest_cluster=1, similarity=0.65, threshold=0.6, min_assign=0.45
Merged segment assigned to cluster 1 (merged_duration=3.6s, merged_sim=0.65)
DEBUG: Updated deferred segment [204.0,205.49] -> spk=1
DEBUG: Got 1 updated segments
DEBUG: Updated segment [204000,205490] -> spk=1
DEBUG: Updating spk_segs entry from spk=-2 to spk=1
```

### 关键日志点

1. **检测到需要合并**：
   ```
   Merging current segment with N deferred segments to get better embedding
   ```

2. **合并音频**：
   ```
   DEBUG: Merged audio - total_samples=X, total_duration=Xs
   ```

3. **重新计算 embedding**：
   ```
   DEBUG: ComputeEmbeddingFromAudio - audio_samples=X, duration=Xs
   ```

4. **合并后的相似度**：
   ```
   DEBUG: Merged embedding similarity - closest_cluster=X, similarity=X
   ```

5. **识别成功**：
   ```
   Merged segment assigned to cluster X (merged_duration=Xs, merged_sim=X)
   ```

6. **更新 deferred 段落**：
   ```
   DEBUG: Updated deferred segment [X,Y] -> spk=Z
   ```

7. **客户端获取更新**：
   ```
   DEBUG: Got N updated segments
   DEBUG: Updating spk_segs entry from spk=-2 to spk=Z
   ```

## 🎯 核心改进

### 1. 智能合并策略

- **第一个短段落**：defer，等待更多信息
- **后续短段落**：立即与之前的 deferred 段落合并，尝试识别
- **合并成功**（相似度足够高）：分配到现有 cluster，更新所有 deferred 段落
- **合并失败但时长足够**（≥2秒）：创建新的 cluster，更新所有 deferred 段落
- **合并失败且时长不够**（<2秒）：继续 defer，等待更多段落

### 2. 音频级别的合并

- ✅ **正确**：拼接原始音频数据，重新计算 embedding
- ❌ **错误**：数学平均 embedding（之前的错误做法）

### 3. 自动创建新 Cluster

- 当合并后的音频 **≥2秒** 时，即使相似度不够高，也会创建新的 cluster
- 避免无限期地 defer 段落
- 确保所有段落最终都能被分配 speaker_id

### 4. 自动更新机制

- 当 deferred 段落被重新识别后，自动添加到 `updated_segments_`
- WebSocket 服务器调用 `GetUpdatedSegments()` 获取更新
- 更新 `spk_segs`，确保客户端收到正确的 speaker_id

## 📝 代码修改

### 文件：`runtime/onnxruntime/src/speaker-diar-model.cpp`

**修改位置**：第 595-705 行

**核心逻辑**：

```cpp
if (duration_sec <= kShortUtteranceDurationSec && max_sim < kMinSimilarityForShortUtterance) {
    if (!deferred_segments_.empty()) {
        // 合并所有 deferred 段落 + 当前段落
        std::vector<float> merged_audio_data;
        for (const auto& deferred : deferred_segments_) {
            merged_audio_data.insert(merged_audio_data.end(),
                                   deferred.audio_data.begin(),
                                   deferred.audio_data.end());
        }
        merged_audio_data.insert(merged_audio_data.end(),
                               audio_data.begin(),
                               audio_data.end());
        
        // 重新计算 embedding
        SpeakerEmbedding merged_embedding = ComputeEmbeddingFromAudio(merged_audio_data);
        
        // 评估相似度
        int merged_closest = FindClosestCluster(merged_embedding);
        float merged_sim = ComputeSimilarity(merged_embedding, state_.centroids[merged_closest]);
        
        if (merged_sim >= state_.similarity_threshold || merged_sim >= kMinSimilarityForAssignment) {
            // 识别成功，更新所有 deferred 段落
            cluster_id = merged_closest;
            for (auto& deferred : deferred_segments_) {
                deferred.speaker_id = cluster_id;
                updated_segments_.push_back(deferred);
            }
            deferred_segments_.clear();
        } else {
            // 仍然失败，继续 defer
            deferred_segments_.push_back(current_segment);
            return -2;
        }
    } else {
        // 第一个 deferred 段落
        deferred_segments_.push_back(current_segment);
        return -2;
    }
}
```

## ✅ 编译状态

所有修改已成功编译：

```
[100%] Built target funasr-wss-server-2pass
```

## 🚀 测试建议

1. **重新运行测试**，使用新编译的 `funasr-wss-server-2pass`
2. **观察日志**，确认合并逻辑正确执行
3. **验证结果**：
   - 连续的短段落应该被合并
   - 合并后的段落应该能够正确识别说话人
   - 如果合并后时长 ≥2秒但相似度仍然低，应该创建新的 cluster
   - 客户端应该收到更新后的 speaker_id
   - **不应该再有无限期的 -2 或 -1**

## 🔧 关键参数

### `kMinDurationForNewCluster = 2.0f`

当合并后的音频时长达到 2 秒时，即使相似度不够高，也会创建新的 cluster。

**原因**：
- 2 秒的音频已经足够提取稳定的 speaker embedding
- 避免无限期地 defer 段落
- 确保所有段落最终都能被分配 speaker_id

**预期行为**：

```
段落1: 1.49s, sim=0.19 → defer
段落2: 2.11s, sim=0.17 → 合并 (total=3.6s, merged_sim=0.20)
  → merged_sim < 0.45 (不够高)
  → 但 total_duration=3.6s ≥ 2.0s ✅
  → 创建新的 cluster 3
  → 段落1 和段落2 都分配到 cluster 3
```

## 📚 相关文档

- `SPEAKER_DIARIZATION_UPDATE_GUIDE.md` - 详细的使用指南
- `IMPLEMENTATION_SUMMARY.md` - 完整的实现总结

