#!/bin/bash

# FunASR WebSocket Server 2-pass startup script for macOS
# This script is adapted from the original run_server_2pass.sh for macOS compatibility

# Get script directory for absolute paths
SCRIPT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Default configuration with absolute paths
download_model_dir="${SCRIPT_ROOT}/../models"
model_dir="damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-onnx"
online_model_dir="damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online-onnx"
vad_dir="damo/speech_fsmn_vad_zh-cn-16k-common-onnx"
punc_dir="damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx"
itn_dir="thuduj12/fst_itn_zh"
lm_dir="damo/speech_ngram_lm_zh-cn-ai-wesp-fst"
spk_dir="${download_model_dir}/damo/speech_campplus_sv_zh-cn_16k-common"
spk_threshold=0.75
confidence_threshold=0.6
enable_error_correction=true
port=10095
certfile=""
keyfile=""
hotword="${SCRIPT_ROOT}/../websocket/hotwords.txt"

# Get CPU count for macOS
decoder_thread_num=$(sysctl -n hw.ncpu) || { echo "Get CPU count failed. Set decoder_thread_num = 8"; decoder_thread_num=8; }
multiple_io=16
io_thread_num=$(( (decoder_thread_num + multiple_io - 1) / multiple_io ))
model_thread_num=1

# Set paths for macOS
script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cmd_path="${script_dir}/websocket/build/bin"
cmd="funasr-wss-server-2pass"

# Simple argument parsing (without external dependencies)
while [[ $# -gt 0 ]]; do
    case $1 in
        --download-model-dir)
            download_model_dir="$2"
            shift 2
            ;;
        --model-dir)
            model_dir="$2"
            shift 2
            ;;
        --online-model-dir)
            online_model_dir="$2"
            shift 2
            ;;
        --vad-dir)
            vad_dir="$2"
            shift 2
            ;;
        --punc-dir)
            punc_dir="$2"
            shift 2
            ;;
        --itn-dir)
            itn_dir="$2"
            shift 2
            ;;
        --lm-dir)
            lm_dir="$2"
            shift 2
            ;;
        --spk-dir)
            spk_dir="$2"
            shift 2
            ;;
        --spk-threshold)
            spk_threshold="$2"
            shift 2
            ;;
        --confidence-threshold)
            confidence_threshold="$2"
            shift 2
            ;;
        --enable-error-correction)
            enable_error_correction="$2"
            shift 2
            ;;
        --port)
            port="$2"
            shift 2
            ;;
        --certfile)
            certfile="$2"
            shift 2
            ;;
        --keyfile)
            keyfile="$2"
            shift 2
            ;;
        --hotword)
            hotword="$2"
            shift 2
            ;;
        --decoder-thread-num)
            decoder_thread_num="$2"
            shift 2
            ;;
        --model-thread-num)
            model_thread_num="$2"
            shift 2
            ;;
        --io-thread-num)
            io_thread_num="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --download-model-dir DIR    Directory to download models (default: ./models)"
            echo "  --model-dir DIR             Offline model directory"
            echo "  --online-model-dir DIR      Online model directory"
            echo "  --vad-dir DIR               VAD model directory"
            echo "  --punc-dir DIR              Punctuation model directory"
            echo "  --itn-dir DIR               ITN model directory"
            echo "  --lm-dir DIR                Language model directory"
            echo "  --spk-dir DIR               Speaker model directory"
            echo "  --spk-threshold FLOAT       Speaker similarity threshold (default: 0.75)"
            echo "  --confidence-threshold FLOAT Confidence threshold for error detection (default: 0.6, enabled by default)"
            echo "  --enable-error-correction BOOL Enable automatic error correction (default: true, enabled by default)"
            echo "  --port PORT                 Server port (default: 10095)"
            echo "  --certfile FILE             SSL certificate file"
            echo "  --keyfile FILE              SSL key file"
            echo "  --hotword FILE              Hotword file"
            echo "  --decoder-thread-num NUM    Number of decoder threads"
            echo "  --model-thread-num NUM      Number of model threads"
            echo "  --io-thread-num NUM         Number of IO threads"
            echo "  --help                      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if executable exists
if [ ! -f "${cmd_path}/${cmd}" ]; then
    echo "Error: ${cmd_path}/${cmd} not found!"
    echo "Please make sure you have compiled the WebSocket server first."
    echo "Run: cd websocket/build && make funasr-wss-server-2pass"
    exit 1
fi

# Create models directory if it doesn't exist
mkdir -p "${download_model_dir}"

# Create hotword file if it doesn't exist
if [ ! -f "${hotword}" ]; then
    mkdir -p "$(dirname "${hotword}")"
    touch "${hotword}"
    echo "Created empty hotword file: ${hotword}"
fi

# Clear SSL options if not provided
if [ -z "$certfile" ] || [ "$certfile" = "0" ]; then
    certfile=""
    keyfile=""
fi

echo "Starting FunASR WebSocket Server 2-pass..."
echo "Configuration:"
echo "  Download model dir: ${download_model_dir}"
echo "  Model dir: ${model_dir}"
echo "  Online model dir: ${online_model_dir}"
echo "  VAD dir: ${vad_dir}"
echo "  Punctuation dir: ${punc_dir}"
echo "  ITN dir: ${itn_dir}"
echo "  LM dir: ${lm_dir}"
echo "  Speaker dir: ${spk_dir}"
echo "  Speaker threshold: ${spk_threshold}"
echo "  Port: ${port}"
echo "  Decoder threads: ${decoder_thread_num}"
echo "  Model threads: ${model_thread_num}"
echo "  IO threads: ${io_thread_num}"
echo "  Hotword file: ${hotword}"
if [ -n "$certfile" ]; then
    echo "  SSL cert: ${certfile}"
    echo "  SSL key: ${keyfile}"
fi
echo ""

# Change to the command directory
cd "${cmd_path}" || exit 1

# Set error correction flag
if [ "${enable_error_correction}" = "true" ]; then
    enable_error_correction_flag="--enable-error-correction"
else
    enable_error_correction_flag=""
fi

# Start the server
"${cmd_path}/${cmd}" \
  --download-model-dir "${download_model_dir}" \
  --model-dir "${model_dir}" \
  --online-model-dir "${online_model_dir}" \
  --vad-dir "${vad_dir}" \
  --punc-dir "${punc_dir}" \
  --itn-dir "${itn_dir}" \
  --lm-dir "${lm_dir}" \
  --spk-dir "${spk_dir}" \
  --spk-threshold "${spk_threshold}" \
  --confidence-threshold "${confidence_threshold}" \
  ${enable_error_correction_flag} \
  --decoder-thread-num ${decoder_thread_num} \
  --model-thread-num ${model_thread_num} \
  --io-thread-num ${io_thread_num} \
  --port ${port} \
  --certfile "${certfile}" \
  --keyfile "${keyfile}" \
  --spk_min_len_ms 500 \
  --hotword "${hotword}" &

server_pid=$!
echo "Server started with PID: ${server_pid}"
echo "Server is running on port ${port}"
echo "To stop the server, run: kill ${server_pid}"

# Wait for the server process
wait ${server_pid}


